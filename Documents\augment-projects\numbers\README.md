# KSA Numbers - محرك بحث الأرقام السعودية

موقع ويب لبحث معلومات الأرقام السعودية مشابه لموقع KSA-Numbers الأصلي.

## المميزات

- 🔍 **بحث سريع**: ابحث عن أي رقم جوال سعودي
- 📱 **تصميم متجاوب**: يعمل على جميع الأجهزة (جوال، تابلت، كمبيوتر)
- 🎨 **تصميم عصري**: واجهة مستخدم جميلة وسهلة الاستخدام
- 📞 **تكامل واتساب**: فتح الرقم مباشرة في واتساب
- ⚡ **سرعة عالية**: استجابة سريعة وتحميل فوري
- 🌐 **دعم اللغة العربية**: مصمم خصيصاً للمستخدمين العرب

## التقنيات المستخدمة

- **HTML5**: هيكل الموقع
- **CSS3**: التصميم والتنسيق
- **JavaScript**: الوظائف التفاعلية
- **Font Awesome**: الأيقونات
- **Google Fonts**: خط Cairo للنصوص العربية

## كيفية الاستخدام

1. افتح الموقع في المتصفح
2. أدخل رقم الجوال السعودي (9 أرقام تبدأ بـ 5)
3. اضغط على زر "بحث"
4. ستظهر النتائج مع إمكانية فتح الرقم في واتساب

## تشغيل الموقع محلياً

### الطريقة الأولى: باستخدام Python
```bash
# انتقل إلى مجلد المشروع
cd Documents/augment-projects/numbers

# شغل الخادم المحلي
python -m http.server 8000

# افتح المتصفح على
http://localhost:8000
```

### الطريقة الثانية: باستخدام Node.js
```bash
# تثبيت http-server عالمياً
npm install -g http-server

# انتقل إلى مجلد المشروع
cd Documents/augment-projects/numbers

# شغل الخادم
http-server -p 8000

# افتح المتصفح على
http://localhost:8000
```

### الطريقة الثالثة: باستخدام Live Server (VS Code)
1. تثبيت إضافة Live Server في VS Code
2. فتح مجلد المشروع في VS Code
3. النقر بالزر الأيمن على index.html
4. اختيار "Open with Live Server"

## هيكل المشروع

```
numbers/
├── index.html          # الصفحة الرئيسية
├── styles.css          # ملف التنسيق
├── script.js           # ملف JavaScript
└── README.md           # ملف التوثيق
```

## أرقام تجريبية للاختبار

يمكنك تجربة البحث باستخدام هذه الأرقام:
- `501234567` - أحمد محمد (STC)
- `551234567` - فاطمة أحمد (Mobily)
- `561234567` - شركة التقنية المتقدمة (Zain)

## التطوير والتخصيص

### إضافة أرقام جديدة
لإضافة أرقام جديدة إلى قاعدة البيانات، عدل ملف `script.js`:

```javascript
const phoneDatabase = {
    '5xxxxxxxx': {
        name: 'الاسم',
        carrier: 'الشبكة',
        region: 'المنطقة',
        type: 'النوع'
    }
};
```

### تخصيص التصميم
يمكنك تعديل ملف `styles.css` لتغيير:
- الألوان
- الخطوط
- التخطيط
- الرسوم المتحركة

### إضافة وظائف جديدة
عدل ملف `script.js` لإضافة:
- تكامل مع APIs خارجية
- حفظ تاريخ البحث
- مشاركة النتائج
- إحصائيات الاستخدام

## الأمان والخصوصية

- الموقع لا يحفظ أي بيانات شخصية
- جميع عمليات البحث تتم محلياً
- لا يتم إرسال البيانات لخوادم خارجية
- الكود مفتوح المصدر وقابل للمراجعة

## المساهمة

نرحب بالمساهمات! يمكنك:
- الإبلاغ عن الأخطاء
- اقتراح مميزات جديدة
- تحسين التصميم
- إضافة ترجمات

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتجاري.

## الدعم

للدعم والاستفسارات:
- البريد الإلكتروني: <EMAIL>
- GitHub Issues: [رابط المشروع]

---

**ملاحظة**: هذا الموقع مصمم للأغراض التعليمية والتوضيحية. في التطبيق الحقيقي، ستحتاج إلى قاعدة بيانات حقيقية وAPI للبحث.
