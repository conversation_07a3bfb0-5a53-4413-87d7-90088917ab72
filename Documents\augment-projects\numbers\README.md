# KSA Numbers - بحث الأرقام السعودية

## 🔍 محرك بحث شامل للأرقام السعودية مع إعلانات Google AdSense

موقع ويب يحتوي على الموقع الأصلي لبحث الأرقام السعودية داخل iframe مع شريط إعلانات Google AdSense للربح.

## 🌐 رابط الموقع المباشر

**[🚀 زيارة الموقع](https://yourusername.github.io/numbers/)**

*استبدل `yourusername` باسم المستخدم الخاص بك في GitHub*

## 📁 ملفات المشروع

### 🏆 الملف الرئيسي:
- **index.html** - الصفحة الرئيسية (تفتح تلقائياً في GitHub Pages)

### 📄 الملفات الإضافية:
- **my-ksa-numbers.html** - نسخة مطابقة للصفحة الرئيسية
- **adsense-setup.html** - نسخة بسيطة مع AdSense
- **premium-wrapper.html** - نسخة متقدمة بميزات إضافية
- **wrapper.html** - نسخة أساسية بدون إعلانات
- **تعليمات-موقعي.md** - دليل الاستخدام باللغة العربية

## 💰 معلومات Google AdSense

- **Publisher ID**: `ca-pub-2682495046966576`
- **Ad Slot ID**: `1234567890` (يجب استبداله بالرقم الحقيقي)
- **نوع الإعلان**: متجاوب (Responsive)

## 🚀 كيفية رفع المشروع على GitHub Pages

### 1. إنشاء Repository جديد:
```bash
# في GitHub، أنشئ repository جديد باسم "numbers"
# أو أي اسم تريده
```

### 2. رفع الملفات:
```bash
git init
git add .
git commit -m "إضافة موقع KSA Numbers مع AdSense"
git branch -M main
git remote add origin https://github.com/yourusername/numbers.git
git push -u origin main
```

### 3. تفعيل GitHub Pages:
1. اذهب إلى Settings في الـ repository
2. انتقل إلى Pages
3. اختر Source: Deploy from a branch
4. اختر Branch: main
5. اختر Folder: / (root)
6. احفظ

### 4. الوصول للموقع:
بعد بضع دقائق، سيكون موقعك متاحاً على:
`https://yourusername.github.io/numbers/`

## ⚙️ إعداد Google AdSense

### الخطوات المطلوبة:
1. **إنشاء وحدة إعلانية** في لوحة تحكم AdSense
2. **الحصول على Ad Slot ID** الحقيقي
3. **استبدال الرقم المؤقت** في الملفات:
   ```html
   <!-- ابحث عن هذا السطر -->
   data-ad-slot="1234567890"
   
   <!-- واستبدله بالرقم الحقيقي -->
   data-ad-slot="YOUR_REAL_AD_SLOT_ID"
   ```

### ملاحظات مهمة:
- ✅ الموقع يجب أن يكون على HTTPS (GitHub Pages يوفر ذلك تلقائياً)
- ✅ Publisher ID صحيح ومُفعل
- ⚠️ لا تنقر على إعلاناتك الخاصة
- ⚠️ انتظر 24-48 ساعة لظهور الإعلانات

## 🎨 المميزات

### ✨ التصميم:
- تصميم عصري ومتجاوب
- دعم كامل للغة العربية (RTL)
- ألوان متدرجة جذابة
- خطوط Cairo الأنيقة

### 🔧 الوظائف:
- عرض الموقع الأصلي داخل iframe آمن
- شريط إعلانات Google AdSense
- أزرار تحكم (تحديث، ملء الشاشة، مشاركة)
- معالجة أخطاء متقدمة

### 📱 التوافق:
- متجاوب 100% مع الجوال
- يعمل على جميع المتصفحات الحديثة
- دعم الوضع المظلم
- تحسين للطباعة

## 📊 تتبع الأداء

### Google Analytics (اختياري):
لإضافة تتبع الزوار، استبدل `GA_MEASUREMENT_ID` في الملفات برقم Google Analytics الخاص بك.

### مراقبة AdSense:
راقب أداء الإعلانات من خلال:
- لوحة تحكم Google AdSense
- تقارير الأرباح اليومية
- إحصائيات النقرات والمشاهدات

## 🛡️ الأمان والخصوصية

- iframe آمن مع sandbox attributes
- لا يتم جمع بيانات شخصية
- امتثال كامل لسياسات Google AdSense
- حماية من البرمجيات الخبيثة

## 📞 الدعم

### مصادر المساعدة:
- [مركز مساعدة AdSense](https://support.google.com/adsense/)
- [دليل GitHub Pages](https://docs.github.com/en/pages)
- [منتدى AdSense](https://support.google.com/adsense/community)

## 📈 نصائح لزيادة الأرباح

1. **جذب زوار حقيقيين** من خلال:
   - مشاركة الموقع في وسائل التواصل
   - تحسين SEO
   - إنشاء محتوى مفيد

2. **تحسين موضع الإعلانات**:
   - ضع الإعلانات في مناطق مرئية
   - اختبر أحجام مختلفة
   - راقب معدل النقر (CTR)

3. **تحسين تجربة المستخدم**:
   - سرعة تحميل عالية
   - تصميم جذاب
   - سهولة الاستخدام

## 📝 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتجاري.

## 🎯 الهدف

الهدف من هذا المشروع هو إنشاء موقع ويب يحتوي على أداة بحث الأرقام السعودية مع إمكانية الربح من خلال إعلانات Google AdSense.

---

## 🚀 البدء السريع

1. **Fork هذا المشروع** أو حمل الملفات
2. **ارفعها على GitHub repository جديد**
3. **فعّل GitHub Pages**
4. **حدث Ad Slot ID** في الملفات
5. **شارك الرابط** واربح! 💰

**بالتوفيق في رحلة الربح من الإنترنت! 🌟**

---

*آخر تحديث: ديسمبر 2024*
