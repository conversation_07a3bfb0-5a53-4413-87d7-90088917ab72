<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KSA Numbers مع إعلانات Google AdSense</title>
    <meta name="description" content="محرك بحث شامل للأرقام السعودية مع إعلانات Google AdSense">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Google AdSense Script -->
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-2682495046966576"
            crossorigin="anonymous"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            direction: rtl;
        }

        /* Header */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #667eea;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .controls {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .control-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            font-family: 'Cairo', Arial, sans-serif;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .control-btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }

        /* Main Content */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        /* iframe Container */
        .iframe-container {
            flex: 1;
            position: relative;
            background: white;
            border-radius: 15px 15px 0 0;
            overflow: hidden;
            margin: 20px;
            margin-bottom: 0;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            min-height: calc(100vh - 200px);
        }

        .main-iframe {
            width: 100%;
            height: 100%;
            border: none;
            display: block;
        }

        /* Ad Container */
        .ad-container {
            background: #f8f9fa;
            border-top: 2px solid #e9ecef;
            padding: 10px;
            text-align: center;
            position: sticky;
            bottom: 0;
            z-index: 50;
            min-height: 120px;
        }

        .ad-label {
            font-size: 0.8rem;
            color: #6c757d;
            margin-bottom: 5px;
            font-weight: 600;
        }

        .ad-content {
            background: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
        }

        /* AdSense Responsive Ad */
        .adsbygoogle {
            display: block;
            min-height: 90px;
        }

        /* Loading State */
        .ad-loading {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 90px;
            color: #6c757d;
            font-size: 0.9rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            .controls {
                flex-wrap: wrap;
                justify-content: center;
            }

            .control-btn {
                padding: 0.4rem 0.8rem;
                font-size: 0.9rem;
            }

            .iframe-container {
                margin: 10px;
                margin-bottom: 0;
                border-radius: 10px 10px 0 0;
                min-height: calc(100vh - 180px);
            }

            .logo {
                font-size: 1.3rem;
            }

            .ad-container {
                min-height: 100px;
            }
        }

        /* Fullscreen Mode */
        .fullscreen .header {
            display: none;
        }

        .fullscreen .iframe-container {
            margin: 0;
            border-radius: 0;
            min-height: calc(100vh - 120px);
        }

        .fullscreen .ad-container {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-search"></i>
                KSA Numbers - بحث الأرقام السعودية
            </div>
            <div class="controls">
                <button class="control-btn" onclick="refreshIframe()">
                    <i class="fas fa-sync-alt"></i>
                    تحديث
                </button>
                <button class="control-btn" onclick="toggleFullscreen()">
                    <i class="fas fa-expand"></i>
                    ملء الشاشة
                </button>
                <button class="control-btn" onclick="openOriginal()">
                    <i class="fas fa-external-link-alt"></i>
                    الموقع الأصلي
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- iframe Container -->
        <div class="iframe-container">
            <iframe 
                id="mainIframe"
                class="main-iframe"
                src="https://storage.googleapis.com/ksa-n/index.html"
                title="KSA Numbers - بحث الأرقام السعودية"
                loading="lazy"
                sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-popups-to-escape-sandbox">
            </iframe>
        </div>
    </main>

    <!-- Ad Container -->
    <div class="ad-container">
        <div class="ad-label">إعلان</div>
        <div class="ad-content">
            <!-- Google AdSense Banner Ad -->
            <ins class="adsbygoogle"
                 style="display:block"
                 data-ad-client="ca-pub-2682495046966576"
                 data-ad-slot="1234567890"
                 data-ad-format="auto"
                 data-full-width-responsive="true"></ins>
            
            <!-- Fallback content if ads don't load -->
            <div class="ad-loading" id="adFallback">
                <i class="fas fa-ad"></i>
                مساحة إعلانية - Google AdSense
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let isFullscreen = false;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 KSA Numbers with AdSense initialized');
            
            // Initialize Google AdSense
            initializeAds();
        });

        // Initialize Google AdSense
        function initializeAds() {
            try {
                // Push ads to AdSense
                (adsbygoogle = window.adsbygoogle || []).push({});
                
                // Hide fallback content after a delay
                setTimeout(() => {
                    const fallback = document.getElementById('adFallback');
                    if (fallback) {
                        fallback.style.display = 'none';
                    }
                }, 3000);
                
                console.log('🎯 AdSense initialized');
            } catch (e) {
                console.log('AdSense initialization error:', e);
            }
        }

        // Refresh iframe
        function refreshIframe() {
            const iframe = document.getElementById('mainIframe');
            iframe.src = iframe.src;
        }

        // Toggle fullscreen
        function toggleFullscreen() {
            const body = document.body;
            const btn = event.target.closest('.control-btn');
            
            isFullscreen = !isFullscreen;
            
            if (isFullscreen) {
                body.classList.add('fullscreen');
                btn.innerHTML = '<i class="fas fa-compress"></i> تصغير';
            } else {
                body.classList.remove('fullscreen');
                btn.innerHTML = '<i class="fas fa-expand"></i> ملء الشاشة';
            }
        }

        // Open original site
        function openOriginal() {
            window.open('https://storage.googleapis.com/ksa-n/index.html', '_blank');
        }

        // Handle window resize for responsive ads
        window.addEventListener('resize', function() {
            // Refresh ads on resize if needed
            if (window.adsbygoogle && window.adsbygoogle.loaded) {
                try {
                    (adsbygoogle = window.adsbygoogle || []).push({});
                } catch (e) {
                    console.log('Ad refresh error:', e);
                }
            }
        });

        // Monitor ad loading
        window.addEventListener('load', function() {
            setTimeout(() => {
                const adElements = document.querySelectorAll('.adsbygoogle');
                adElements.forEach(ad => {
                    if (ad.innerHTML.trim() === '') {
                        console.log('⚠️ Ad may not have loaded properly');
                        // Show fallback or handle empty ad
                        const fallback = document.getElementById('adFallback');
                        if (fallback) {
                            fallback.style.display = 'flex';
                            fallback.innerHTML = '<i class="fas fa-exclamation-triangle"></i> لم يتم تحميل الإعلان';
                        }
                    }
                });
            }, 5000);
        });
    </script>
</body>
</html>
