/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    direction: rtl;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 20px;
}

.logo h1 {
    color: #667eea;
    font-size: 1.8rem;
    font-weight: 700;
}

.logo i {
    margin-left: 10px;
}

.nav {
    display: flex;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 600;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #667eea;
}

/* Warning Notice */
.warning-notice {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    margin: 20px 0;
}

.warning-content {
    padding: 20px;
    text-align: center;
}

.warning-content h3 {
    color: #856404;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.warning-content p {
    color: #856404;
    margin: 5px 0;
    font-size: 0.95rem;
}

/* Main Content */
.main {
    flex: 1;
    padding: 2rem 0;
}

/* Welcome Section */
.welcome-section {
    text-align: center;
    margin-bottom: 3rem;
    color: white;
}

.welcome-section h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* Search Section */
.search-section {
    margin-bottom: 3rem;
}

.search-container {
    max-width: 600px;
    margin: 0 auto;
}

.search-box {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    margin-bottom: 2rem;
}

.input-group {
    display: flex;
    align-items: center;
    gap: 0;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    overflow: hidden;
    transition: border-color 0.3s ease;
}

.input-group:focus-within {
    border-color: #667eea;
}

.country-code {
    background: #f8f9fa;
    padding: 1rem;
    font-weight: 600;
    color: #666;
    border-left: 1px solid #e0e0e0;
}

.phone-input {
    flex: 1;
    padding: 1rem;
    border: none;
    outline: none;
    font-size: 1.1rem;
    font-family: 'Cairo', Arial, sans-serif;
    direction: ltr;
    text-align: center;
}

.search-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Cairo', Arial, sans-serif;
}

.search-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.search-btn i {
    margin-left: 8px;
}

/* Loading */
.loading {
    text-align: center;
    color: white;
    font-size: 1.2rem;
    padding: 1rem;
}

.loading i {
    margin-left: 10px;
    font-size: 1.5rem;
}

/* Results Section */
.results-section {
    margin-bottom: 3rem;
}

.results-container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.results-container h3 {
    color: #333;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    text-align: center;
}

.result-item {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    border-right: 4px solid #667eea;
}

.result-item h4 {
    color: #333;
    margin-bottom: 0.5rem;
}

.result-item p {
    color: #666;
    margin-bottom: 0.5rem;
}

.whatsapp-btn {
    background: #25d366;
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    font-family: 'Cairo', Arial, sans-serif;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-top: 1rem;
}

.whatsapp-btn:hover {
    background: #128c7e;
    transform: translateY(-2px);
}

.whatsapp-btn i {
    margin-left: 8px;
}

/* Contact and Disclaimer */
.contact-section, .disclaimer {
    text-align: center;
    color: white;
    margin: 2rem 0;
}

.email {
    color: #ffd700;
    font-weight: 600;
}

.disclaimer {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Footer */
.footer {
    background: rgba(0, 0, 0, 0.2);
    color: white;
    text-align: center;
    padding: 1rem 0;
    margin-top: auto;
}

/* Utility Classes */
.hidden {
    display: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header .container {
        flex-direction: column;
        gap: 1rem;
    }
    
    .welcome-section h2 {
        font-size: 2rem;
    }
    
    .search-box {
        padding: 1.5rem;
    }
    
    .input-group {
        flex-direction: column;
    }
    
    .country-code {
        border-left: none;
        border-bottom: 1px solid #e0e0e0;
    }
    
    .search-btn {
        width: 100%;
    }
    
    .nav {
        gap: 1rem;
    }
}
