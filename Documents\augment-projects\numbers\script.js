// DOM Elements
const phoneInput = document.getElementById('phoneInput');
const searchBtn = document.getElementById('searchBtn');
const loading = document.getElementById('loading');
const results = document.getElementById('results');
const resultContent = document.getElementById('resultContent');

// Database will be loaded from JSON file
let phoneDatabase = {};
let carriersData = {};
let regionsData = [];
let statisticsData = {};

// Load database from JSON file
async function loadDatabase() {
    try {
        const response = await fetch('database.json');
        const data = await response.json();

        phoneDatabase = data.numbers;
        carriersData = data.carriers;
        regionsData = data.regions;
        statisticsData = data.statistics;

        console.log('تم تحميل قاعدة البيانات بنجاح:', statisticsData.totalNumbers, 'رقم');
        return true;
    } catch (error) {
        console.error('خطأ في تحميل قاعدة البيانات:', error);
        // Fallback to sample data
        phoneDatabase = {
            '501234567': {
                name: 'أحمد محمد',
                carrier: 'STC',
                region: 'الرياض',
                type: 'شخصي',
                verified: true
            }
        };
        return false;
    }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Add event listeners
    searchBtn.addEventListener('click', handleSearch);
    phoneInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            handleSearch();
        }
    });
    
    // Input validation and formatting
    phoneInput.addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, ''); // Remove non-digits
        
        // Limit to 9 digits
        if (value.length > 9) {
            value = value.substring(0, 9);
        }
        
        // Ensure it starts with 5
        if (value.length > 0 && value[0] !== '5') {
            value = '5' + value.substring(1);
        }
        
        e.target.value = value;
    });
    
    // Focus on input when page loads
    phoneInput.focus();
}

async function handleSearch() {
    const phoneNumber = phoneInput.value.trim();
    
    // Validate input
    if (!validatePhoneNumber(phoneNumber)) {
        showError('يرجى إدخال رقم جوال صحيح (9 أرقام تبدأ بـ 5)');
        return;
    }
    
    // Show loading
    showLoading(true);
    hideResults();
    
    try {
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // Search for the number
        const result = await searchPhoneNumber(phoneNumber);
        
        // Hide loading
        showLoading(false);
        
        // Display results
        displayResults(phoneNumber, result);
        
    } catch (error) {
        showLoading(false);
        showError('حدث خطأ أثناء البحث. يرجى المحاولة مرة أخرى.');
        console.error('Search error:', error);
    }
}

function validatePhoneNumber(phoneNumber) {
    // Check if it's exactly 9 digits and starts with 5
    const phoneRegex = /^5\d{8}$/;
    return phoneRegex.test(phoneNumber);
}

async function searchPhoneNumber(phoneNumber) {
    // In a real application, this would make an API call to your backend
    // For demonstration, we'll use the sample data
    
    const result = phoneDatabase[phoneNumber];
    
    if (result) {
        return {
            found: true,
            data: result
        };
    } else {
        // Generate some mock data for demonstration
        return {
            found: false,
            suggestions: generateSuggestions(phoneNumber)
        };
    }
}

function generateSuggestions(phoneNumber) {
    const carriers = ['STC', 'Mobily', 'Zain'];
    const regions = ['الرياض', 'جدة', 'الدمام', 'مكة', 'المدينة'];
    
    return {
        carrier: carriers[Math.floor(Math.random() * carriers.length)],
        region: regions[Math.floor(Math.random() * regions.length)],
        prefix: phoneNumber.substring(0, 3)
    };
}

function displayResults(phoneNumber, result) {
    let html = '';
    
    if (result.found) {
        // Display found result
        html = `
            <div class="result-item">
                <h4><i class="fas fa-user"></i> ${result.data.name}</h4>
                <p><i class="fas fa-mobile-alt"></i> الرقم: +966${phoneNumber}</p>
                <p><i class="fas fa-building"></i> الشبكة: ${result.data.carrier}</p>
                <p><i class="fas fa-map-marker-alt"></i> المنطقة: ${result.data.region}</p>
                <p><i class="fas fa-tag"></i> النوع: ${result.data.type}</p>
                <button class="whatsapp-btn" onclick="openWhatsApp('${phoneNumber}')">
                    <i class="fab fa-whatsapp"></i>
                    فتح في واتساب
                </button>
            </div>
        `;
    } else {
        // Display not found message with suggestions
        html = `
            <div class="result-item">
                <h4><i class="fas fa-exclamation-circle"></i> لم يتم العثور على معلومات لهذا الرقم</h4>
                <p><i class="fas fa-mobile-alt"></i> الرقم المبحوث عنه: +966${phoneNumber}</p>
                <p><i class="fas fa-building"></i> الشبكة المحتملة: ${result.suggestions.carrier}</p>
                <p><i class="fas fa-map-marker-alt"></i> المنطقة المحتملة: ${result.suggestions.region}</p>
                <p><i class="fas fa-info-circle"></i> بادئة الرقم: ${result.suggestions.prefix}</p>
                <button class="whatsapp-btn" onclick="openWhatsApp('${phoneNumber}')">
                    <i class="fab fa-whatsapp"></i>
                    فتح في واتساب
                </button>
            </div>
        `;
    }
    
    resultContent.innerHTML = html;
    showResults();
}

function openWhatsApp(phoneNumber) {
    const whatsappUrl = `https://api.whatsapp.com/send?phone=966${phoneNumber}`;
    window.open(whatsappUrl, '_blank');
}

function showLoading(show) {
    if (show) {
        loading.classList.remove('hidden');
    } else {
        loading.classList.add('hidden');
    }
}

function showResults() {
    results.classList.remove('hidden');
    results.scrollIntoView({ behavior: 'smooth' });
}

function hideResults() {
    results.classList.add('hidden');
}

function showError(message) {
    resultContent.innerHTML = `
        <div class="result-item" style="border-right-color: #e74c3c;">
            <h4 style="color: #e74c3c;"><i class="fas fa-exclamation-triangle"></i> خطأ</h4>
            <p>${message}</p>
        </div>
    `;
    showResults();
}

// Add some sample numbers for testing
function addSampleNumbers() {
    const samples = [
        '501234567',
        '551234567', 
        '561234567',
        '507777777',
        '558888888'
    ];
    
    console.log('أرقام تجريبية للاختبار:', samples);
}

// Initialize sample numbers
addSampleNumbers();

// Add keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + K to focus search
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        phoneInput.focus();
        phoneInput.select();
    }
    
    // Escape to clear results
    if (e.key === 'Escape') {
        hideResults();
        phoneInput.focus();
    }
});

// Add smooth scrolling for better UX
function smoothScroll(target) {
    document.querySelector(target).scrollIntoView({
        behavior: 'smooth'
    });
}
